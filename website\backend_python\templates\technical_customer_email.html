<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Technical Support Request Received - {{ brand }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            font-weight: 600;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 18px;
            margin-bottom: 20px;
            color: #dc2626;
            font-weight: 600;
        }
        
        .message {
            font-size: 16px;
            margin-bottom: 30px;
            line-height: 1.8;
        }
        
        .ticket-info {
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
            text-align: center;
        }
        
        .ticket-id {
            font-size: 24px;
            font-weight: bold;
            color: #dc2626;
            margin-bottom: 10px;
        }
        
        .ticket-priority {
            display: inline-block;
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .priority-high {
            background-color: #fee2e2;
            color: #dc2626;
            border: 1px solid #fecaca;
        }
        
        .priority-medium {
            background-color: #fef3c7;
            color: #d97706;
            border: 1px solid #fde68a;
        }
        
        .priority-low {
            background-color: #ecfdf5;
            color: #059669;
            border: 1px solid #d1fae5;
        }
        
        .info-card {
            background-color: #f8fafc;
            border-left: 4px solid #dc2626;
            padding: 20px;
            margin: 25px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .info-card h3 {
            color: #dc2626;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .info-row {
            display: flex;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }
        
        .info-label {
            font-weight: 600;
            color: #374151;
            min-width: 140px;
            margin-right: 10px;
        }
        
        .info-value {
            color: #6b7280;
            flex: 1;
        }
        
        .problem-description {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .problem-description h3 {
            color: #92400e;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .problem-description p {
            color: #92400e;
            font-style: italic;
            line-height: 1.6;
        }
        
        .next-steps {
            background-color: #f0f9ff;
            border: 1px solid #7dd3fc;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
        }
        
        .next-steps h3 {
            color: #0369a1;
            margin-bottom: 15px;
            font-size: 18px;
        }
        
        .next-steps ul {
            color: #0369a1;
            padding-left: 20px;
        }
        
        .next-steps li {
            margin-bottom: 8px;
        }
        
        .contact-info {
            background-color: #f1f5f9;
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
        }
        
        .contact-info h3 {
            color: #dc2626;
            margin-bottom: 20px;
            font-size: 20px;
        }
        
        .contact-details {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
        }
        
        .contact-item {
            text-align: center;
            flex: 1;
            min-width: 150px;
        }
        
        .contact-item strong {
            display: block;
            color: #374151;
            margin-bottom: 5px;
        }
        
        .contact-item a {
            color: #dc2626;
            text-decoration: none;
        }
        
        .contact-item a:hover {
            text-decoration: underline;
        }
        
        .rating-section {
            background-color: #fef7ff;
            border: 1px solid #e879f9;
            border-radius: 8px;
            padding: 20px;
            margin: 30px 0;
            text-align: center;
        }
        
        .rating-stars {
            color: #f59e0b;
            font-size: 24px;
            margin: 10px 0;
        }
        
        .footer {
            background-color: #374151;
            color: #d1d5db;
            padding: 30px 20px;
            text-align: center;
            font-size: 14px;
        }
        
        .footer p {
            margin-bottom: 10px;
        }
        
        .footer a {
            color: #fca5a5;
            text-decoration: none;
        }
        
        .footer a:hover {
            text-decoration: underline;
        }
        
        .social-links {
            margin: 20px 0;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 10px;
            padding: 8px 15px;
            background-color: #4b5563;
            color: #ffffff;
            text-decoration: none;
            border-radius: 5px;
            font-size: 12px;
        }
        
        .social-links a:hover {
            background-color: #dc2626;
        }
        
        @media (max-width: 600px) {
            .container {
                margin: 0;
                box-shadow: none;
            }
            
            .content {
                padding: 20px 15px;
            }
            
            .header {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .contact-details {
                flex-direction: column;
                gap: 15px;
            }
            
            .info-row {
                flex-direction: column;
            }
            
            .info-label {
                min-width: auto;
                margin-bottom: 5px;
            }
            
            .ticket-id {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>Technical Support</h1>
            <p>{{ brand }} - Service Excellence</p>
        </div>
        
        <!-- Main Content -->
        <div class="content">
            <div class="greeting">
                Dear {{ contact_person }},
            </div>
            
            <div class="message">
                <p>Thank you for contacting {{ brand }} Technical Support! We have successfully received your service request and our technical team is already reviewing your case.</p>
                
                <p>Your request regarding <strong>{{ model }}</strong> (Serial: {{ serial_number }}) has been logged in our system and assigned to our specialized {{ service_center }} service center team.</p>
            </div>
            
            <!-- Ticket Information -->
            <div class="ticket-info">
                <div class="ticket-id">{{ ticket_id }}</div>
                <p style="color: #6b7280; margin-bottom: 15px;">Your Support Ticket Number</p>
                <div class="ticket-priority priority-{{ priority_level }}">
                    {{ priority_level }} Priority
                </div>
            </div>
            
            <!-- Request Summary -->
            <div class="info-card">
                <h3>📋 Service Request Summary</h3>
                <div class="info-row">
                    <span class="info-label">Product Category:</span>
                    <span class="info-value">{{ product_category }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Brand:</span>
                    <span class="info-value">{{ brand }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Model:</span>
                    <span class="info-value">{{ model }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Serial Number:</span>
                    <span class="info-value">{{ serial_number }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Service Center:</span>
                    <span class="info-value">{{ service_center }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Call Type:</span>
                    <span class="info-value">{{ call_type }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Category:</span>
                    <span class="info-value">{{ call_category }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Current Status:</span>
                    <span class="info-value">{{ call_condition }}</span>
                </div>
                {% if has_preferred_time %}
                <div class="info-row">
                    <span class="info-label">Preferred Time:</span>
                    <span class="info-value">{{ preferred_time }}</span>
                </div>
                {% endif %}
                <div class="info-row">
                    <span class="info-label">Submitted:</span>
                    <span class="info-value">{{ formatted_submission_date }}</span>
                </div>
            </div>
            
            <!-- Customer Information -->
            <div class="info-card">
                <h3>👤 Contact Information</h3>
                <div class="info-row">
                    <span class="info-label">Company:</span>
                    <span class="info-value">{{ company }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Contact Person:</span>
                    <span class="info-value">{{ contact_person }}</span>
                </div>
                {% if has_alternative_contact %}
                <div class="info-row">
                    <span class="info-label">Alternative Contact:</span>
                    <span class="info-value">{{ alternative_contact }}</span>
                </div>
                {% endif %}
                <div class="info-row">
                    <span class="info-label">Mobile:</span>
                    <span class="info-value">{{ mobile_number }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Email:</span>
                    <span class="info-value">{{ email }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Address:</span>
                    <span class="info-value">{{ company_address }}</span>
                </div>
            </div>
            
            <!-- Problem Description -->
            <div class="problem-description">
                <h3>🔧 Problem Description</h3>
                <p>"{{ problem_description }}"</p>
            </div>
            
            <!-- Rating Section (if provided) -->
            {% if has_rating %}
            <div class="rating-section">
                <h3 style="color: #a21caf; margin-bottom: 15px;">⭐ Product Rating</h3>
                <div class="rating-stars">
                    {% for i in range(rating) %}★{% endfor %}{% for i in range(5 - rating) %}☆{% endfor %}
                </div>
                <p style="color: #a21caf;">{{ rating }}/5 stars</p>
            </div>
            {% endif %}
            
            <!-- Next Steps -->
            <div class="next-steps">
                <h3>🚀 What Happens Next?</h3>
                <ul>
                    <li><strong>Immediate:</strong> Your request has been logged and assigned ticket #{{ ticket_id }}</li>
                    <li><strong>Within {{ estimated_response_time | default("2-4 hours") }}:</strong> Our technical team will contact you for initial assessment</li>
                    {% if preferred_time != "Not specified" %}
                    <li><strong>Preferred Time:</strong> We'll try to contact you during your preferred time: {{ preferred_time }}</li>
                    {% endif %}
                    <li><strong>Service Coordination:</strong> {{ service_center }} service center will coordinate the resolution process</li>
                    <li><strong>Resolution Updates:</strong> You'll receive regular updates via email and SMS</li>
                    <li><strong>Completion:</strong> Final service report and satisfaction survey</li>
                </ul>
            </div>
            
            <!-- Contact Information -->
            <div class="contact-info">
                <h3>📞 Need Immediate Support?</h3>
                <p style="margin-bottom: 20px; color: #6b7280;">Our technical support team is available 24/7</p>
                
                <div class="contact-details">
                    <div class="contact-item">
                        <strong>📧 Email</strong>
                        <a href="mailto:support@{{ brand_raw }}.com">support@{{ brand_raw }}.com</a>
                    </div>
                    <div class="contact-item">
                        <strong>📱 Helpline</strong>
                        <a href="tel:+911800123456">1800-123-456</a>
                    </div>
                    <div class="contact-item">
                        <strong>💬 Live Chat</strong>
                        <a href="https://{{ brand_raw }}.com/support" target="_blank">Online Support</a>
                    </div>
                </div>
                
                <div style="margin-top: 20px; color: #6b7280;">
                    <strong>📍 {{ service_center }} Service Center:</strong><br>
                    Contact your local service center for on-site support
                </div>
            </div>
            
            <div class="message">
                <p>We understand how important your equipment is to your operations, and we're committed to resolving your issue as quickly as possible. Our experienced technicians at {{ service_center }} are equipped to handle all {{ brand }} products and will ensure minimal downtime.</p>
                
                <p style="margin-top: 20px;">Please keep your ticket number <strong>{{ ticket_id }}</strong> handy for all future communications regarding this request.</p>
                
                <p style="margin-top: 20px;"><strong>Best regards,</strong><br>
                {{ brand }} Technical Support Team</p>
            </div>
        </div>
        
        <!-- Footer -->
        <div class="footer">
            <p><strong>{{ brand }} Technical Support</strong></p>
            <p>Excellence in Service & Support</p>
            
            <div class="social-links">
                <a href="https://{{ brand_raw }}.com" target="_blank">Website</a>
                <a href="mailto:support@{{ brand_raw }}.com">Email Support</a>
                <a href="tel:+911800123456">Call Support</a>
            </div>
            
            <p>24/7 Technical Support Available</p>
            <p>&copy; {{ current_year }} {{ brand }}. All rights reserved.</p>
            
            <p style="font-size: 12px; margin-top: 15px; color: #9ca3af;">
                This email was sent because you submitted a technical support request. 
                Your ticket number is {{ ticket_id }}. For support, contact us at support@{{ brand_raw }}.com
            </p>
        </div>
    </div>
</body>
</html>